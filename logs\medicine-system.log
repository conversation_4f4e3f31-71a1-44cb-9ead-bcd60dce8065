2025-08-01 15:54:48.624  INFO 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 8628 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 15:54:48.632 DEBUG 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 15:54:48.633  INFO 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 15:54:48.700  INFO 8628 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 15:54:48.701  INFO 8628 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 15:54:49.525  INFO 8628 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 15:54:49.620  INFO 8628 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 86 ms. Found 11 JPA repository interfaces.
2025-08-01 15:54:50.347  INFO 8628 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 15:54:50.359  INFO 8628 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 15:54:50.360  INFO 8628 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 15:54:50.436  INFO 8628 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 15:54:50.437  INFO 8628 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1736 ms
2025-08-01 15:54:50.682  INFO 8628 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 15:54:50.748  INFO 8628 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 15:54:50.957  INFO 8628 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 15:54:51.076  INFO 8628 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 15:54:51.597  INFO 8628 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 15:54:51.617  INFO 8628 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 15:54:52.504  INFO 8628 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 15:54:52.519  INFO 8628 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 15:54:52.634  WARN 8628 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 15:54:53.427  WARN 8628 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: b2d6fc56-2ad4-45d0-aa82-f2b99666ac23

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:54:53.568  INFO 8628 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@436e4242, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c37cd24, org.springframework.security.web.context.SecurityContextPersistenceFilter@6e562c83, org.springframework.security.web.header.HeaderWriterFilter@23d78b44, org.springframework.web.filter.CorsFilter@5597cd67, org.springframework.security.web.authentication.logout.LogoutFilter@25c5ccb8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@692caabf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2a33f195, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48745cc5, org.springframework.security.web.session.SessionManagementFilter@26568ca7, org.springframework.security.web.access.ExceptionTranslationFilter@71de3b02, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6aee9af0]
2025-08-01 15:54:53.961  INFO 8628 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 15:54:54.004  INFO 8628 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 15:54:54.014  INFO 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.848 seconds (JVM running for 10.396)
2025-08-01 15:55:31.123  INFO 8628 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:55:31.124  INFO 8628 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 15:55:31.130  INFO 8628 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-08-01 16:03:48.953  INFO 8628 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:03:48.961  INFO 8628 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 16:03:48.979  INFO 8628 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 16:03:59.110  INFO 12844 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 12844 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 16:03:59.111 DEBUG 12844 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 16:03:59.112  INFO 12844 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 16:03:59.180  INFO 12844 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 16:03:59.180  INFO 12844 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 16:04:00.923  INFO 12844 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 16:04:01.034  INFO 12844 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 103 ms. Found 11 JPA repository interfaces.
2025-08-01 16:04:01.771  INFO 12844 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 16:04:01.780  INFO 12844 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 16:04:01.781  INFO 12844 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 16:04:01.865  INFO 12844 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 16:04:01.866  INFO 12844 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2684 ms
2025-08-01 16:04:02.090  INFO 12844 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 16:04:02.143  INFO 12844 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 16:04:02.325  INFO 12844 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 16:04:02.445  INFO 12844 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 16:04:02.913  INFO 12844 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 16:04:02.933  INFO 12844 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 16:04:03.743  INFO 12844 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 16:04:03.753  INFO 12844 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:04:03.815  WARN 12844 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 16:04:04.607  WARN 12844 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 3be017d8-ac6e-4a5b-a0dc-9f05909e187c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:04:04.742  INFO 12844 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1b0475b1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6bb3a790, org.springframework.security.web.context.SecurityContextPersistenceFilter@6b2e1e3, org.springframework.security.web.header.HeaderWriterFilter@3a972f65, org.springframework.web.filter.CorsFilter@7dcb172a, org.springframework.security.web.authentication.logout.LogoutFilter@452a6882, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5a9373d1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@266866e1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1372d512, org.springframework.security.web.session.SessionManagementFilter@305fe361, org.springframework.security.web.access.ExceptionTranslationFilter@4edb4eea, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@90bac9c]
2025-08-01 16:04:05.133  INFO 12844 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 16:04:05.175  INFO 12844 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 16:04:05.185  INFO 12844 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.53 seconds (JVM running for 12.14)
2025-08-01 16:04:32.856  INFO 12844 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:04:32.856  INFO 12844 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 16:04:32.858  INFO 12844 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-01 16:08:53.854  INFO 12844 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:08:53.863  INFO 12844 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 16:08:53.875  INFO 12844 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 16:08:59.454  INFO 21144 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 21144 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 16:08:59.455 DEBUG 21144 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 16:08:59.456  INFO 21144 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 16:08:59.555  INFO 21144 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 16:08:59.556  INFO 21144 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 16:09:00.837  INFO 21144 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 16:09:00.983  INFO 21144 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 134 ms. Found 11 JPA repository interfaces.
2025-08-01 16:09:01.875  INFO 21144 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 16:09:01.891  INFO 21144 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 16:09:01.892  INFO 21144 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 16:09:01.970  INFO 21144 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 16:09:01.971  INFO 21144 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2414 ms
2025-08-01 16:09:02.205  INFO 21144 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 16:09:02.268  INFO 21144 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 16:09:02.464  INFO 21144 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 16:09:02.580  INFO 21144 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 16:09:03.040  INFO 21144 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 16:09:03.063  INFO 21144 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 16:09:03.970  INFO 21144 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 16:09:03.984  INFO 21144 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:09:04.049  WARN 21144 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 16:09:04.935  WARN 21144 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 7142567f-7b5e-4a50-b906-6dfa8203ac73

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:09:05.106  INFO 21144 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e1997d5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b6b8aac, org.springframework.security.web.context.SecurityContextPersistenceFilter@61100110, org.springframework.security.web.header.HeaderWriterFilter@6ef18cca, org.springframework.web.filter.CorsFilter@4fab8780, org.springframework.security.web.authentication.logout.LogoutFilter@687dc6e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@181cc1c1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@533dfad8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@678d774a, org.springframework.security.web.session.SessionManagementFilter@2a0e52ef, org.springframework.security.web.access.ExceptionTranslationFilter@a74c20b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@41376671]
2025-08-01 16:09:05.531  INFO 21144 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 16:09:05.571  INFO 21144 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 16:09:05.583  INFO 21144 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.731 seconds (JVM running for 7.97)
2025-08-01 16:09:53.381  INFO 21144 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:09:53.381  INFO 21144 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 16:09:53.383  INFO 21144 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 16:20:16.240  INFO 21144 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:20:16.245  INFO 21144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 16:20:16.260  INFO 21144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 16:20:20.948  INFO 21000 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 21000 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 16:20:20.949 DEBUG 21000 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 16:20:20.950  INFO 21000 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 16:20:21.037  INFO 21000 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 16:20:21.037  INFO 21000 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 16:20:22.040  INFO 21000 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 16:20:22.148  INFO 21000 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 96 ms. Found 11 JPA repository interfaces.
2025-08-01 16:20:22.938  INFO 21000 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 16:20:22.956  INFO 21000 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 16:20:22.956  INFO 21000 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 16:20:23.045  INFO 21000 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 16:20:23.046  INFO 21000 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2008 ms
2025-08-01 16:20:23.292  INFO 21000 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 16:20:23.374  INFO 21000 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 16:20:23.605  INFO 21000 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 16:20:23.746  INFO 21000 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 16:20:24.352  INFO 21000 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 16:20:24.376  INFO 21000 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 16:20:25.300  INFO 21000 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 16:20:25.316  INFO 21000 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:20:25.380  WARN 21000 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 16:20:26.254  WARN 21000 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 0d6f7b5a-b2d1-4a59-a2c7-8c25dafa9609

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:20:26.419  INFO 21000 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@63fe402b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@17e88abf, org.springframework.security.web.context.SecurityContextPersistenceFilter@7137a538, org.springframework.security.web.header.HeaderWriterFilter@b057f88, org.springframework.web.filter.CorsFilter@31e5d9fe, org.springframework.security.web.authentication.logout.LogoutFilter@44212b8d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1873e8df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@313996df, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@34c21f54, org.springframework.security.web.session.SessionManagementFilter@49d80e7f, org.springframework.security.web.access.ExceptionTranslationFilter@41c0132f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6b6782a4]
2025-08-01 16:20:26.834  INFO 21000 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 16:20:26.874  INFO 21000 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 16:20:26.886  INFO 21000 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.55 seconds (JVM running for 7.662)
2025-08-01 16:20:31.564  INFO 21000 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:20:31.564  INFO 21000 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 16:20:31.567  INFO 21000 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-01 16:32:17.131  INFO 21000 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:32:17.135  INFO 21000 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 16:32:17.145  INFO 21000 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 16:32:21.967  INFO 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 9396 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 16:32:21.969 DEBUG 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 16:32:21.969  INFO 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 16:32:22.051  INFO 9396 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 16:32:22.051  INFO 9396 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 16:32:23.056  INFO 9396 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 16:32:23.178  INFO 9396 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 110 ms. Found 11 JPA repository interfaces.
2025-08-01 16:32:24.104  INFO 9396 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 16:32:24.125  INFO 9396 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 16:32:24.126  INFO 9396 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 16:32:24.215  INFO 9396 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 16:32:24.216  INFO 9396 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2164 ms
2025-08-01 16:32:24.474  INFO 9396 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 16:32:24.543  INFO 9396 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 16:32:24.779  INFO 9396 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 16:32:24.906  INFO 9396 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 16:32:25.367  INFO 9396 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 16:32:25.382  INFO 9396 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 16:32:26.171  INFO 9396 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 16:32:26.184  INFO 9396 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:32:26.242  WARN 9396 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 16:32:26.980  WARN 9396 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 679ea1cd-8fd8-43e0-b2c0-be08e4b5ce9a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:32:27.099  INFO 9396 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5b22bf8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1583cd4e, org.springframework.security.web.context.SecurityContextPersistenceFilter@6405f1a6, org.springframework.security.web.header.HeaderWriterFilter@3fd49485, org.springframework.web.filter.CorsFilter@b59635e, org.springframework.security.web.authentication.logout.LogoutFilter@56df028a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@68833d4d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1ea7aedd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b51828a, org.springframework.security.web.session.SessionManagementFilter@4899e0f9, org.springframework.security.web.access.ExceptionTranslationFilter@73a24772, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@77ceabeb]
2025-08-01 16:32:27.446  INFO 9396 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 16:32:27.496  INFO 9396 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 16:32:27.507  INFO 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.104 seconds (JVM running for 7.37)
2025-08-01 16:32:32.128  INFO 9396 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:32:32.129  INFO 9396 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 16:32:32.131  INFO 9396 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-01 16:38:13.674  INFO 9396 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:38:13.679  INFO 9396 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 16:38:13.694  INFO 9396 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 16:38:31.537  INFO 23140 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 23140 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 16:38:31.539 DEBUG 23140 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 16:38:31.540  INFO 23140 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 16:38:31.627  INFO 23140 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 16:38:31.628  INFO 23140 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 16:38:32.621  INFO 23140 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 16:38:32.792  INFO 23140 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 156 ms. Found 11 JPA repository interfaces.
2025-08-01 16:38:33.884  INFO 23140 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 16:38:33.904  INFO 23140 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 16:38:33.905  INFO 23140 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 16:38:34.003  INFO 23140 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 16:38:34.003  INFO 23140 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2375 ms
2025-08-01 16:38:34.258  INFO 23140 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 16:38:34.325  INFO 23140 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 16:38:34.511  INFO 23140 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 16:38:34.620  INFO 23140 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 16:38:35.093  INFO 23140 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 16:38:35.111  INFO 23140 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 16:38:36.092  INFO 23140 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 16:38:36.102  INFO 23140 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:38:36.163  WARN 23140 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 16:38:36.953  WARN 23140 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 52178010-bb6d-4830-a21d-a102f62d2ebf

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:38:37.081  INFO 23140 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7ef77e2c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5016a129, org.springframework.security.web.context.SecurityContextPersistenceFilter@6eeb2998, org.springframework.security.web.header.HeaderWriterFilter@7b3b76a5, org.springframework.web.filter.CorsFilter@17d77d20, org.springframework.security.web.authentication.logout.LogoutFilter@8f69140, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@c7292dc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@445fbf3a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48ba9d1d, org.springframework.security.web.session.SessionManagementFilter@2ba543e8, org.springframework.security.web.access.ExceptionTranslationFilter@6c4931c5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e871a9c]
2025-08-01 16:38:37.456  INFO 23140 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 16:38:37.507  INFO 23140 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 16:38:37.518  INFO 23140 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.559 seconds (JVM running for 7.707)
2025-08-01 16:38:55.197  INFO 23140 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:38:55.198  INFO 23140 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 16:38:55.201  INFO 23140 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-01 16:46:26.857  INFO 23140 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:46:26.863  INFO 23140 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 16:46:26.874  INFO 23140 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 16:46:37.356  INFO 3032 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 3032 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 16:46:37.357 DEBUG 3032 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 16:46:37.357  INFO 3032 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 16:46:37.444  INFO 3032 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 16:46:37.444  INFO 3032 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 16:46:38.282  INFO 3032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 16:46:38.399  INFO 3032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 108 ms. Found 11 JPA repository interfaces.
2025-08-01 16:46:39.032  INFO 3032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 16:46:39.047  INFO 3032 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 16:46:39.047  INFO 3032 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 16:46:39.118  INFO 3032 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 16:46:39.119  INFO 3032 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1674 ms
2025-08-01 16:46:39.331  INFO 3032 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 16:46:39.390  INFO 3032 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 16:46:39.543  INFO 3032 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 16:46:39.648  INFO 3032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 16:46:40.042  INFO 3032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 16:46:40.061  INFO 3032 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 16:46:40.788  INFO 3032 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 16:46:40.799  INFO 3032 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 16:46:40.853  WARN 3032 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 16:46:41.544  WARN 3032 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 4a474215-a39a-4f05-9259-817ec0581700

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 16:46:41.641  INFO 3032 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@66ab04b4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c682032, org.springframework.security.web.context.SecurityContextPersistenceFilter@198859ad, org.springframework.security.web.header.HeaderWriterFilter@15a8f306, org.springframework.web.filter.CorsFilter@45ec13e6, org.springframework.security.web.authentication.logout.LogoutFilter@56aecd19, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@85c3587, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b23e195, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7419e866, org.springframework.security.web.session.SessionManagementFilter@612ca151, org.springframework.security.web.access.ExceptionTranslationFilter@7e3818da, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@42b7c330]
2025-08-01 16:46:42.110  INFO 3032 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 16:46:42.166  INFO 3032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 16:46:42.179  INFO 3032 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.46 seconds (JVM running for 6.545)
2025-08-01 16:46:58.238  INFO 3032 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 16:46:58.238  INFO 3032 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 16:46:58.240  INFO 3032 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 17:00:45.193  INFO 3032 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 17:00:45.196  INFO 3032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 17:00:45.203  INFO 3032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 19:09:53.292  INFO 23196 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 23196 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 19:09:53.294 DEBUG 23196 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 19:09:53.296  INFO 23196 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 19:09:53.413  INFO 23196 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 19:09:53.414  INFO 23196 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 19:09:54.495  INFO 23196 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 19:09:54.639  INFO 23196 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 130 ms. Found 11 JPA repository interfaces.
2025-08-01 19:09:55.672  INFO 23196 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 19:09:55.692  INFO 23196 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 19:09:55.693  INFO 23196 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 19:09:55.788  INFO 23196 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 19:09:55.789  INFO 23196 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2374 ms
2025-08-01 19:09:56.074  INFO 23196 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 19:09:56.154  INFO 23196 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 19:09:56.376  INFO 23196 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 19:09:56.506  INFO 23196 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 19:09:57.074  INFO 23196 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 19:09:57.092  INFO 23196 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 19:09:58.065  INFO 23196 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 19:09:58.077  INFO 23196 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 19:09:58.156  WARN 23196 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 19:09:59.225  WARN 23196 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: f0026f77-814f-4474-bf74-285577a8477c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 19:09:59.422  INFO 23196 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4aa9dcf5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@77fd1c22, org.springframework.security.web.context.SecurityContextPersistenceFilter@775cab97, org.springframework.security.web.header.HeaderWriterFilter@572a9d83, org.springframework.web.filter.CorsFilter@522b5218, org.springframework.security.web.authentication.logout.LogoutFilter@6eb3e62d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@510f3f75, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2b01e4a6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@694adae3, org.springframework.security.web.session.SessionManagementFilter@1d2a45e4, org.springframework.security.web.access.ExceptionTranslationFilter@de0c0df, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6faec08]
2025-08-01 19:10:00.045  INFO 23196 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 19:10:00.095  INFO 23196 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 19:10:00.106  INFO 23196 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 7.404 seconds (JVM running for 8.597)
2025-08-01 19:10:27.027  INFO 23196 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 19:10:27.027  INFO 23196 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 19:10:27.030  INFO 23196 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-01 19:16:09.553  INFO 23196 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 19:16:09.558  INFO 23196 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 19:16:09.576  INFO 23196 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 19:16:21.584  INFO 23336 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 23336 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 19:16:21.586 DEBUG 23336 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 19:16:21.588  INFO 23336 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 19:16:21.701  INFO 23336 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 19:16:21.702  INFO 23336 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 19:16:22.997  INFO 23336 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 19:16:23.170  INFO 23336 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 156 ms. Found 11 JPA repository interfaces.
2025-08-01 19:16:24.050  INFO 23336 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 19:16:24.068  INFO 23336 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 19:16:24.068  INFO 23336 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 19:16:24.154  INFO 23336 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 19:16:24.155  INFO 23336 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2452 ms
2025-08-01 19:16:24.403  INFO 23336 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 19:16:24.460  INFO 23336 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 19:16:24.666  INFO 23336 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 19:16:24.814  INFO 23336 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 19:16:25.263  INFO 23336 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 19:16:25.278  INFO 23336 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 19:16:26.064  INFO 23336 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 19:16:26.073  INFO 23336 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 19:16:26.139  WARN 23336 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 19:16:26.977  WARN 23336 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: bc593236-c0ba-43a5-8934-3aa5212bb10c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 19:16:27.156  INFO 23336 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@ed58a0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b5cf2ec, org.springframework.security.web.context.SecurityContextPersistenceFilter@7afaad3f, org.springframework.security.web.header.HeaderWriterFilter@32efc64f, org.springframework.web.filter.CorsFilter@102ca739, org.springframework.security.web.authentication.logout.LogoutFilter@28633722, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@aec5aa0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7337fbf, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@73c6707d, org.springframework.security.web.session.SessionManagementFilter@4ad2915e, org.springframework.security.web.access.ExceptionTranslationFilter@627c4cd5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7c919242]
2025-08-01 19:16:27.618  INFO 23336 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 19:16:27.664  INFO 23336 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 19:16:27.674  INFO 23336 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.681 seconds (JVM running for 8.126)
2025-08-01 19:16:53.696  INFO 23336 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 19:16:53.698  INFO 23336 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 19:16:53.701  INFO 23336 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 19:31:18.209  INFO 23336 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 19:31:18.215  INFO 23336 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 19:31:18.229  INFO 23336 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 19:31:28.509  INFO 12760 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 12760 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 19:31:28.511 DEBUG 12760 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 19:31:28.512  INFO 12760 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 19:31:28.595  INFO 12760 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 19:31:28.595  INFO 12760 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 19:31:29.703  INFO 12760 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 19:31:29.876  INFO 12760 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 159 ms. Found 11 JPA repository interfaces.
2025-08-01 19:31:30.733  INFO 12760 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 19:31:30.745  INFO 12760 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 19:31:30.745  INFO 12760 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 19:31:30.826  INFO 12760 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 19:31:30.826  INFO 12760 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2230 ms
2025-08-01 19:31:31.053  INFO 12760 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 19:31:31.114  INFO 12760 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 19:31:31.284  INFO 12760 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 19:31:31.400  INFO 12760 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 19:31:31.864  INFO 12760 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 19:31:31.880  INFO 12760 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 19:31:32.679  INFO 12760 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 19:31:32.689  INFO 12760 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 19:31:32.743  WARN 12760 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 19:31:33.658  WARN 12760 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a7fd1930-e2ce-4ed0-8d7a-2f4f231eb027

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 19:31:33.799  INFO 12760 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@737c158, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@567cf71a, org.springframework.security.web.context.SecurityContextPersistenceFilter@3865a0d6, org.springframework.security.web.header.HeaderWriterFilter@7e3818da, org.springframework.web.filter.CorsFilter@66ab04b4, org.springframework.security.web.authentication.logout.LogoutFilter@54990e8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@198859ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3816c5fd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c682032, org.springframework.security.web.session.SessionManagementFilter@48f6867f, org.springframework.security.web.access.ExceptionTranslationFilter@34ed2006, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@35d4cc4f]
2025-08-01 19:31:34.146  INFO 12760 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 19:31:34.189  INFO 12760 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 19:31:34.200  INFO 12760 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.225 seconds (JVM running for 7.281)
2025-08-01 19:32:08.903  INFO 12760 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 19:32:08.904  INFO 12760 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 19:32:08.907  INFO 12760 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-01 20:06:46.835  INFO 12760 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 20:06:46.845  INFO 12760 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 20:06:46.851  INFO 12760 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 20:06:51.139  INFO 4036 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 4036 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 20:06:51.141 DEBUG 4036 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 20:06:51.142  INFO 4036 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 20:06:51.224  INFO 4036 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 20:06:51.225  INFO 4036 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 20:06:52.063  INFO 4036 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 20:06:52.158  INFO 4036 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 87 ms. Found 11 JPA repository interfaces.
2025-08-01 20:06:52.733  INFO 4036 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 20:06:52.749  INFO 4036 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 20:06:52.750  INFO 4036 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 20:06:52.830  INFO 4036 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 20:06:52.830  INFO 4036 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1605 ms
2025-08-01 20:06:53.033  INFO 4036 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 20:06:53.081  INFO 4036 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 20:06:53.269  INFO 4036 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 20:06:53.402  INFO 4036 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 20:06:53.832  INFO 4036 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 20:06:53.849  INFO 4036 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 20:06:54.709  INFO 4036 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 20:06:54.721  INFO 4036 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 20:06:54.776  WARN 4036 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 20:06:55.597  WARN 4036 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: cc4c54c9-d572-4a3b-84a6-cbd2f323f4a4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 20:06:55.709  INFO 4036 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@737c158, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@567cf71a, org.springframework.security.web.context.SecurityContextPersistenceFilter@3865a0d6, org.springframework.security.web.header.HeaderWriterFilter@7e3818da, org.springframework.web.filter.CorsFilter@66ab04b4, org.springframework.security.web.authentication.logout.LogoutFilter@54990e8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@198859ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3816c5fd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c682032, org.springframework.security.web.session.SessionManagementFilter@48f6867f, org.springframework.security.web.access.ExceptionTranslationFilter@34ed2006, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@35d4cc4f]
2025-08-01 20:06:56.086  INFO 4036 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 20:06:56.124  INFO 4036 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 20:06:56.134  INFO 4036 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.574 seconds (JVM running for 6.59)
2025-08-01 20:07:00.599  INFO 4036 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 20:07:00.600  INFO 4036 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 20:07:00.604  INFO 4036 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-01 20:19:38.834  WARN 4036 --- [http-nio-8080-exec-5] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported]
2025-08-01 20:22:39.374  WARN 4036 --- [http-nio-8080-exec-7] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported]
2025-08-01 20:28:20.548  INFO 4036 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 20:28:20.551  INFO 4036 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 20:28:20.563  INFO 4036 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 20:28:29.869  INFO 14484 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 14484 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 20:28:29.871 DEBUG 14484 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 20:28:29.872  INFO 14484 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 20:28:29.958  INFO 14484 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 20:28:29.959  INFO 14484 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 20:28:30.855  INFO 14484 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 20:28:30.956  INFO 14484 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 93 ms. Found 11 JPA repository interfaces.
2025-08-01 20:28:31.739  INFO 14484 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 20:28:31.753  INFO 14484 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 20:28:31.754  INFO 14484 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 20:28:31.822  INFO 14484 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 20:28:31.822  INFO 14484 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1863 ms
2025-08-01 20:28:32.026  INFO 14484 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 20:28:32.083  INFO 14484 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 20:28:32.269  INFO 14484 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 20:28:32.383  INFO 14484 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 20:28:32.898  INFO 14484 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 20:28:32.912  INFO 14484 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 20:28:33.645  INFO 14484 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 20:28:33.656  INFO 14484 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 20:28:33.710  WARN 14484 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 20:28:34.477  WARN 14484 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: e6027c34-b9fa-4ca7-9e36-c2b31768fb8f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 20:28:34.607  INFO 14484 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4e13a544, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@35d2cf37, org.springframework.security.web.context.SecurityContextPersistenceFilter@40ad8bd1, org.springframework.security.web.header.HeaderWriterFilter@2a15952d, org.springframework.web.filter.CorsFilter@184c135d, org.springframework.security.web.authentication.logout.LogoutFilter@40029fab, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@25554b79, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@d33d825, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ebe0a3e, org.springframework.security.web.session.SessionManagementFilter@5b457af4, org.springframework.security.web.access.ExceptionTranslationFilter@193a845a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2a4a4ef1]
2025-08-01 20:28:34.967  INFO 14484 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 20:28:35.006  INFO 14484 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 20:28:35.017  INFO 14484 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.708 seconds (JVM running for 6.734)
2025-08-01 20:28:53.142  INFO 14484 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 20:28:53.142  INFO 14484 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 20:28:53.152  INFO 14484 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 10 ms
2025-08-01 20:37:16.754  WARN 14484 --- [http-nio-8080-exec-1] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "records"]
2025-08-01 20:43:31.362  WARN 14484 --- [http-nio-8080-exec-3] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "records"]
2025-08-01 20:44:09.824  INFO 14484 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 20:44:09.828  INFO 14484 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 20:44:09.844  INFO 14484 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 20:44:20.410  INFO 3420 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 3420 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 20:44:20.412 DEBUG 3420 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 20:44:20.413  INFO 3420 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 20:44:20.507  INFO 3420 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 20:44:20.507  INFO 3420 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 20:44:21.595  INFO 3420 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 20:44:21.720  INFO 3420 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 114 ms. Found 12 JPA repository interfaces.
2025-08-01 20:44:22.338  INFO 3420 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 20:44:22.350  INFO 3420 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 20:44:22.350  INFO 3420 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 20:44:22.429  INFO 3420 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 20:44:22.430  INFO 3420 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1923 ms
2025-08-01 20:44:22.618  INFO 3420 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 20:44:22.676  INFO 3420 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 20:44:22.836  INFO 3420 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 20:44:22.939  INFO 3420 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 20:44:23.370  INFO 3420 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 20:44:23.386  INFO 3420 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 20:44:24.231  INFO 3420 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 20:44:24.243  INFO 3420 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 20:44:24.305  WARN 3420 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 20:44:25.077  WARN 3420 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 6dae0759-5a0d-4271-babc-d4382fe6efa0

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 20:44:25.220  INFO 3420 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@63c745ad, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3badaeae, org.springframework.security.web.context.SecurityContextPersistenceFilter@2180fa72, org.springframework.security.web.header.HeaderWriterFilter@624a9e27, org.springframework.web.filter.CorsFilter@28a9e48, org.springframework.security.web.authentication.logout.LogoutFilter@3b12e331, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@21444e9a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@93720f1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6bb44cf0, org.springframework.security.web.session.SessionManagementFilter@64baa3cf, org.springframework.security.web.access.ExceptionTranslationFilter@5bc03f94, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@27ac04c1]
2025-08-01 20:44:25.592  INFO 3420 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 20:44:25.632  INFO 3420 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 20:44:25.643  INFO 3420 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.86 seconds (JVM running for 7.073)
2025-08-01 20:45:08.785  INFO 3420 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 20:45:08.785  INFO 3420 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 20:45:08.787  INFO 3420 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-08-01 21:02:29.961  INFO 3420 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 21:02:29.965  INFO 3420 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01 21:02:29.973  INFO 3420 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01 21:02:33.629  INFO 17676 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 17676 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 21:02:33.631 DEBUG 17676 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 21:02:33.632  INFO 17676 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 21:02:33.718  INFO 17676 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 21:02:33.718  INFO 17676 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 21:02:34.511  INFO 17676 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 21:02:34.618  INFO 17676 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 97 ms. Found 12 JPA repository interfaces.
2025-08-01 21:02:35.240  INFO 17676 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 21:02:35.252  INFO 17676 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 21:02:35.252  INFO 17676 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 21:02:35.314  INFO 17676 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 21:02:35.314  INFO 17676 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1594 ms
2025-08-01 21:02:35.482  INFO 17676 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 21:02:35.533  INFO 17676 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 21:02:35.673  INFO 17676 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 21:02:35.778  INFO 17676 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 21:02:36.160  INFO 17676 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 21:02:36.175  INFO 17676 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 21:02:36.862  INFO 17676 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 21:02:36.872  INFO 17676 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 21:02:36.924  WARN 17676 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 21:02:37.724  WARN 17676 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 0ab2237c-61ea-4a6c-868c-f873ae8af4ce

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 21:02:37.872  INFO 17676 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7793c790, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@34cc8683, org.springframework.security.web.context.SecurityContextPersistenceFilter@280bee25, org.springframework.security.web.header.HeaderWriterFilter@3a66f88, org.springframework.web.filter.CorsFilter@3b221233, org.springframework.security.web.authentication.logout.LogoutFilter@3d9ec9c2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3556bf09, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@49859e28, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21833ec1, org.springframework.security.web.session.SessionManagementFilter@1409efd7, org.springframework.security.web.access.ExceptionTranslationFilter@6036a563, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@31e6d695]
2025-08-01 21:02:38.369  INFO 17676 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 21:02:38.411  INFO 17676 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 21:02:38.421  INFO 17676 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.31 seconds (JVM running for 6.287)
2025-08-01 21:02:42.610  INFO 17676 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 21:02:42.610  INFO 17676 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 21:02:42.611  INFO 17676 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
