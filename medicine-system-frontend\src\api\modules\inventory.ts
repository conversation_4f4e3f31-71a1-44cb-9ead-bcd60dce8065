import request from '../index';
import type { Inventory, InventoryRecord, PageParams, PageResult } from '@/types';

// 获取库存列表
export const getInventoryList = (params: PageParams) => {
  return request.get<PageResult<Inventory>>('/inventory/list', { params });
};

// 获取库存详情
export const getInventoryDetail = (id: number) => {
  return request.get<Inventory>(`/inventory/${id}`);
};

// 入库操作
export const inventoryIn = (data: {
  medicineId: number;
  warehouseId: number;
  quantity: number;
  reason: string;
}) => {
  return request.post('/inventory/in', data);
};

// 出库操作
export const inventoryOut = (data: {
  medicineId: number;
  warehouseId: number;
  quantity: number;
  reason: string;
}) => {
  return request.post('/inventory/out', data);
};

// 获取库存记录
export const getInventoryRecords = (params: PageParams & {
  medicineId?: number;
  type?: string;
  operator?: string;
  startDate?: string;
  endDate?: string;
  medicineName?: string;
}) => {
  return request.get<PageResult<InventoryRecord>>('/inventory/records', { params });
};

// 库存盘点
export const inventoryCheck = (data: {
  medicineId: number;
  warehouseId: number;
  actualQuantity: number;
  reason: string;
}) => {
  return request.post('/inventory/check', data);
};

// 获取库存预警列表
export const getLowStockInventory = () => {
  return request.get<Inventory[]>('/inventory/low-stock');
};